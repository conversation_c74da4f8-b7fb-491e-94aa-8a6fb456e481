# 酒店中英文名称对照数据库（极简版）

## 项目简介

基于携程和飞猪的酒店数据，创建统一的酒店中英文名称对照数据库。**极简化输出，只保留中英文名称字段。**

## 主要功能

- 基于中文酒店名称进行去重判断（完全匹配）
- 智能合并两个平台的数据信息
- 携程数据优先，飞猪数据补充缺失字段
- **极简输出**：只保留 `chinese_name` 和 `english_name` 两个字段

## 文件结构

```
├── hotel_database_merger.py    # 主模块文件
├── config.py                   # 配置文件
├── test_merger.py             # 测试脚本
├── README.md                  # 说明文档
├── unified_hotel_database.json # 输出的统一数据库
└── hotel_merger.log           # 处理日志
```

## 使用方法

### 1. 运行主程序

```bash
python hotel_database_merger.py
```

### 2. 运行测试

```bash
python test_merger.py
```

### 3. 自定义配置

编辑 `config.py` 文件来修改：
- 输入文件路径
- 输出文件设置
- 数据清洗规则
- 合并优先级

## 输出格式（极简版）

生成的 `unified_hotel_database.json` 文件包含极简化的酒店信息：

```json
[
  {
    "chinese_name": "中文酒店名称",
    "english_name": "English Hotel Name"
  },
  {
    "chinese_name": "另一个酒店",
    "english_name": ""
  }
]
```

### 字段说明

- **chinese_name**: 标准化后的中文酒店名称
- **english_name**: 清洗后的英文酒店名称（缺失时为空字符串""）

## 处理统计

程序运行后会显示详细的处理统计信息：

```
携程酒店总数: 448
飞猪酒店总数: 3752
去重后酒店总数: 3893
合并的重复酒店数: 307
缺失英文名称的酒店数: 397
```

## 技术特性

### 🔧 数据清洗算法
- 使用正则表达式精确提取中英文名称
- 智能识别和标准化评分格式
- 自动处理空白字符和特殊字符

### 🔄 去重合并策略
- 基于中文名称的精确匹配去重
- 携程数据优先的合并策略
- 保持数据完整性的补充机制

### 📝 日志记录
- 详细的处理过程日志
- 错误和警告信息记录
- 性能统计和处理时间跟踪

### ✅ 质量保证
- 完整的单元测试覆盖
- 数据格式验证
- 异常处理和错误恢复

## 依赖要求

- Python 3.6+
- 标准库：json, hashlib, re, os, logging

## 许可证

本项目仅供学习和研究使用。

## 更新日志

### v1.0.0 (2025-07-24)
- 初始版本发布
- 实现基本的数据整合和去重功能
- 支持携程和飞猪数据源
- 完整的测试套件和文档
