#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
酒店数据库整合模块测试脚本

测试功能：
1. 数据清洗功能测试
2. 去重合并功能测试
3. 输出格式验证
4. 统计信息验证

作者：AI Assistant
创建日期：2025-07-24
"""

import json
import os
from hotel_database_merger import HotelDatabaseMerger


def test_data_cleaning():
    """测试数据清洗功能"""
    print("🧪 测试数据清洗功能...")
    
    merger = HotelDatabaseMerger()
    
    # 测试携程数据清洗
    ctrip_test_data = [
        {
            "hotelName": "广明公园科穆恩生活与健康酒店",
            "hotel-subtitle": "Hotel Komune Living & Wellness Kuala Lumpur",
            "score": "4.5",
            "position-desc": "近敦拉萨镇地铁站"
        },
        {
            "hotelName": "吉隆坡机场图恩酒店",
            "hotel-subtitle": "Tune Hotel KLIA Aeropolis (Airport Hotel)",
            "score": "3.7",
            "position-desc": "雪邦 · 近Eraman Duty Free（吉隆坡国际机场T1店） · 吉隆坡国际机场地区"
        }
    ]
    
    cleaned_ctrip = merger.clean_ctrip_data(ctrip_test_data)
    print(f"✅ 携程数据清洗测试通过，处理了 {len(cleaned_ctrip)} 条记录")
    
    # 测试飞猪数据清洗
    fliggy_test_data = [
        {
            "row-title": "吉隆坡希尔顿花园酒店北店(Hilton Garden Inn Kuala Lumpur - North)",
            "ename": "(Hilton Garden Inn Kuala Lumpur - North)",
            "score": "4.3/5分优秀",
            "area": "位于太子世界贸易中心"
        },
        {
            "row-title": "太平洋丝绸酒店 (The Pacific Sutera Hotel)",
            "ename": "(The Pacific Sutera Hotel)",
            "score": "4.3/5分优秀",
            "area": "位于亚庇市中心"
        }
    ]
    
    cleaned_fliggy = merger.clean_fliggy_data(fliggy_test_data)
    print(f"✅ 飞猪数据清洗测试通过，处理了 {len(cleaned_fliggy)} 条记录")
    
    return cleaned_ctrip, cleaned_fliggy


def test_text_extraction():
    """测试文本提取功能"""
    print("\n🧪 测试文本提取功能...")
    
    merger = HotelDatabaseMerger()
    
    # 测试中文名称提取
    test_titles = [
        "吉隆坡希尔顿花园酒店北店(Hilton Garden Inn Kuala Lumpur - North)",
        "太平洋丝绸酒店 (The Pacific Sutera Hotel)",
        "普通酒店名称"
    ]
    
    for title in test_titles:
        chinese_name = merger.extract_chinese_name(title)
        print(f"原标题: {title}")
        print(f"提取的中文名称: {chinese_name}")
    
    # 测试英文名称提取
    test_enames = [
        "(Hilton Garden Inn Kuala Lumpur - North)",
        "(The Pacific Sutera Hotel)",
        "Hotel Name Without Brackets"
    ]
    
    for ename in test_enames:
        english_name = merger.extract_english_name(ename)
        print(f"原英文名: {ename}")
        print(f"提取的英文名称: {english_name}")
    
    # 测试评分提取
    test_scores = [
        "4.3/5分优秀",
        "2/5分一般",
        "4.8/5分卓越",
        "无效评分"
    ]
    
    for score_str in test_scores:
        score = merger.extract_score(score_str)
        print(f"原评分: {score_str}")
        print(f"提取的评分: {score}")
    
    print("✅ 文本提取功能测试通过")


def test_unique_id_generation():
    """测试唯一ID生成功能"""
    print("\n🧪 测试唯一ID生成功能...")
    
    merger = HotelDatabaseMerger()
    
    test_names = [
        "广明公园科穆恩生活与健康酒店",
        "吉隆坡机场图恩酒店",
        "吉隆坡希尔顿花园酒店北店"
    ]
    
    for name in test_names:
        unique_id = merger.generate_unique_id(name)
        print(f"酒店名称: {name}")
        print(f"生成的ID: {unique_id}")
    
    # 测试相同名称生成相同ID
    id1 = merger.generate_unique_id("测试酒店")
    id2 = merger.generate_unique_id("测试酒店")
    assert id1 == id2, "相同名称应该生成相同的ID"
    
    print("✅ 唯一ID生成功能测试通过")


def test_merge_functionality():
    """测试合并功能"""
    print("\n🧪 测试合并功能...")
    
    merger = HotelDatabaseMerger()
    
    # 测试数据合并
    hotel1 = {
        'chinese_name': '测试酒店',
        'english_name': 'Test Hotel',
        'score': 4.5,
        'location': '测试位置',
        'source': 'ctrip'
    }
    
    hotel2 = {
        'chinese_name': '测试酒店',
        'english_name': '',
        'score': None,
        'location': '飞猪位置',
        'source': 'fliggy'
    }
    
    merged = merger.merge_hotel_data(hotel1, hotel2)
    print(f"合并结果: {merged}")
    
    assert merged['source'] == 'merged', "合并后的source应该是'merged'"
    assert merged['english_name'] == 'Test Hotel', "应该保留携程的英文名称"
    assert merged['score'] == 4.5, "应该保留携程的评分"
    
    print("✅ 合并功能测试通过")


def validate_output_format():
    """验证输出格式"""
    print("\n🧪 验证输出格式...")
    
    if not os.path.exists('unified_hotel_database.json'):
        print("❌ 输出文件不存在，请先运行主程序")
        return False
    
    try:
        with open('unified_hotel_database.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            print("❌ 输出数据应该是列表格式")
            return False
        
        if len(data) == 0:
            print("❌ 输出数据为空")
            return False
        
        # 检查第一条记录的格式
        first_record = data[0]
        required_fields = ['id', 'chinese_name', 'english_name', 'score', 'location', 'source']
        
        for field in required_fields:
            if field not in first_record:
                print(f"❌ 缺少必要字段: {field}")
                return False
        
        print(f"✅ 输出格式验证通过，共 {len(data)} 条记录")
        print(f"📋 示例记录: {json.dumps(first_record, ensure_ascii=False, indent=2)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证输出格式时出错: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始运行酒店数据库整合模块测试...")
    print("=" * 60)
    
    try:
        # 运行各项测试
        test_data_cleaning()
        test_text_extraction()
        test_unique_id_generation()
        test_merge_functionality()
        
        print("\n" + "=" * 60)
        print("🎉 所有单元测试通过！")
        
        # 验证输出格式（如果输出文件存在）
        validate_output_format()
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        raise


if __name__ == "__main__":
    main()
