#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
酒店中英文名称对照数据库整合模块
基于携程和飞猪的酒店数据，创建统一的酒店中英文名称对照数据库

主要功能：
1. 数据整合与去重（基于中文酒店名称）
2. 字段映射与清洗
3. 生成唯一ID（MD5哈希）
4. 输出标准化JSON格式

作者：AI Assistant
创建日期：2025-07-24
"""

import json
import hashlib
import re
import os
import logging
from typing import Dict, List, Optional, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hotel_merger.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class HotelDatabaseMerger:
    """酒店数据库整合器"""
    
    def __init__(self):
        """初始化整合器"""
        self.ctrip_files = [
            'ctrip-2025-07-21.json',
            'ctrip-2025-07-22.json'
        ]
        self.fliggy_files = [
            'fliggy-2025-07-22.json',
            'fliggy-2025-07-22 (1).json'
        ]
        self.hotels_dict = {}  # 以中文名称为key的酒店字典
        self.stats = {
            'ctrip_total': 0,
            'fliggy_total': 0,
            'merged_total': 0,
            'duplicates_merged': 0,
            'missing_english_names': 0
        }
    
    def load_json_file(self, filepath: str) -> List[Dict]:
        """加载JSON文件"""
        try:
            if not os.path.exists(filepath):
                logger.warning(f"文件不存在: {filepath}")
                return []
            
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"成功加载文件: {filepath}, 记录数: {len(data)}")
                return data
        except Exception as e:
            logger.error(f"加载文件失败 {filepath}: {str(e)}")
            return []
    
    def extract_chinese_name(self, title: str) -> str:
        """从飞猪标题中提取中文名称（去除括号及括号内容）"""
        if not title:
            return ""
        
        # 去除括号及括号内的英文内容
        pattern = r'^([^(]+)(?:\s*\([^)]*\))?$'
        match = re.match(pattern, title.strip())
        if match:
            return match.group(1).strip()
        return title.strip()
    
    def extract_english_name(self, ename: str) -> str:
        """从飞猪ename中提取英文名称（去除外层括号）"""
        if not ename:
            return ""
        
        # 去除外层括号
        pattern = r'^\((.+)\)$'
        match = re.match(pattern, ename.strip())
        if match:
            return match.group(1).strip()
        return ename.strip()
    
    def extract_score(self, score_str: str) -> Optional[float]:
        """从飞猪评分字符串中提取数字评分"""
        if not score_str:
            return None
        
        # 提取数字部分，如"4.3/5分优秀" -> 4.3
        pattern = r'^(\d+(?:\.\d+)?)'
        match = re.match(pattern, score_str.strip())
        if match:
            try:
                return float(match.group(1))
            except ValueError:
                return None
        return None
    
    def generate_unique_id(self, chinese_name: str) -> str:
        """基于中文名称生成MD5哈希作为唯一ID"""
        if not chinese_name:
            return ""
        
        # 对中文名称进行UTF-8编码后生成MD5哈希
        return hashlib.md5(chinese_name.encode('utf-8')).hexdigest()
    
    def clean_ctrip_data(self, data: List[Dict]) -> List[Dict]:
        """清洗携程数据"""
        cleaned_data = []
        
        for item in data:
            try:
                chinese_name = item.get('hotelName', '').strip()
                if not chinese_name:
                    continue
                
                # 提取并清洗字段
                english_name = item.get('hotel-subtitle', '').strip()
                score_str = item.get('score', '')
                score = float(score_str) if score_str and score_str.replace('.', '').isdigit() else None
                location = item.get('position-desc', '').strip()
                
                hotel_data = {
                    'chinese_name': chinese_name,
                    'english_name': english_name,
                    'score': score,
                    'location': location,
                    'source': 'ctrip'
                }
                
                cleaned_data.append(hotel_data)
                
            except Exception as e:
                logger.warning(f"清洗携程数据时出错: {str(e)}")
                continue
        
        self.stats['ctrip_total'] += len(cleaned_data)
        logger.info(f"携程数据清洗完成，有效记录: {len(cleaned_data)}")
        return cleaned_data
    
    def clean_fliggy_data(self, data: List[Dict]) -> List[Dict]:
        """清洗飞猪数据"""
        cleaned_data = []
        
        for item in data:
            try:
                raw_title = item.get('row-title', '').strip()
                if not raw_title:
                    continue
                
                # 提取中文名称（去除括号内容）
                chinese_name = self.extract_chinese_name(raw_title)
                if not chinese_name:
                    continue
                
                # 提取英文名称（去除外层括号）
                raw_ename = item.get('ename', '').strip()
                english_name = self.extract_english_name(raw_ename)
                
                # 提取评分
                raw_score = item.get('score', '').strip()
                score = self.extract_score(raw_score)
                
                # 位置信息
                location = item.get('area', '').strip()
                
                hotel_data = {
                    'chinese_name': chinese_name,
                    'english_name': english_name,
                    'score': score,
                    'location': location,
                    'source': 'fliggy'
                }
                
                cleaned_data.append(hotel_data)
                
            except Exception as e:
                logger.warning(f"清洗飞猪数据时出错: {str(e)}")
                continue
        
        self.stats['fliggy_total'] += len(cleaned_data)
        logger.info(f"飞猪数据清洗完成，有效记录: {len(cleaned_data)}")
        return cleaned_data

    def merge_hotel_data(self, hotel1: Dict, hotel2: Dict) -> Dict:
        """合并两个酒店数据，携程数据优先"""
        merged = hotel1.copy()

        # 如果携程数据缺失某些字段，用飞猪数据补充
        if not merged.get('english_name') and hotel2.get('english_name'):
            merged['english_name'] = hotel2['english_name']

        if merged.get('score') is None and hotel2.get('score') is not None:
            merged['score'] = hotel2['score']

        if not merged.get('location') and hotel2.get('location'):
            merged['location'] = hotel2['location']

        # 确定数据来源标记
        if hotel1.get('source') == 'ctrip' and hotel2.get('source') == 'fliggy':
            merged['source'] = 'merged'
        elif hotel1.get('source') == 'fliggy' and hotel2.get('source') == 'ctrip':
            # 如果第一个是飞猪数据，第二个是携程数据，优先使用携程数据
            merged = hotel2.copy()
            if not merged.get('english_name') and hotel1.get('english_name'):
                merged['english_name'] = hotel1['english_name']
            if merged.get('score') is None and hotel1.get('score') is not None:
                merged['score'] = hotel1['score']
            if not merged.get('location') and hotel1.get('location'):
                merged['location'] = hotel1['location']
            merged['source'] = 'merged'

        return merged

    def process_all_data(self):
        """处理所有数据文件"""
        logger.info("开始处理所有数据文件...")

        # 处理携程数据
        for filepath in self.ctrip_files:
            raw_data = self.load_json_file(filepath)
            if raw_data:
                cleaned_data = self.clean_ctrip_data(raw_data)
                for hotel in cleaned_data:
                    chinese_name = hotel['chinese_name']
                    if chinese_name in self.hotels_dict:
                        # 发现重复，合并数据
                        self.hotels_dict[chinese_name] = self.merge_hotel_data(
                            self.hotels_dict[chinese_name], hotel
                        )
                        self.stats['duplicates_merged'] += 1
                    else:
                        self.hotels_dict[chinese_name] = hotel

        # 处理飞猪数据
        for filepath in self.fliggy_files:
            raw_data = self.load_json_file(filepath)
            if raw_data:
                cleaned_data = self.clean_fliggy_data(raw_data)
                for hotel in cleaned_data:
                    chinese_name = hotel['chinese_name']
                    if chinese_name in self.hotels_dict:
                        # 发现重复，合并数据
                        self.hotels_dict[chinese_name] = self.merge_hotel_data(
                            self.hotels_dict[chinese_name], hotel
                        )
                        self.stats['duplicates_merged'] += 1
                    else:
                        self.hotels_dict[chinese_name] = hotel

        self.stats['merged_total'] = len(self.hotels_dict)
        logger.info(f"数据处理完成，去重后酒店总数: {self.stats['merged_total']}")

    def generate_final_database(self) -> List[Dict]:
        """生成最终的标准化数据库"""
        final_database = []

        for chinese_name, hotel_data in self.hotels_dict.items():
            # 检查英文名称是否缺失
            english_name = hotel_data.get('english_name', '').strip()
            if not english_name:
                english_name = ""
                self.stats['missing_english_names'] += 1

            # 构建最终记录 - 极简化版本，只保留中英文名字
            final_record = {
                'chinese_name': chinese_name,
                'english_name': english_name
            }

            final_database.append(final_record)

        # 按中文名称排序
        final_database.sort(key=lambda x: x['chinese_name'])

        logger.info(f"最终数据库生成完成，记录数: {len(final_database)}")
        return final_database

    def save_to_json(self, data: List[Dict], filename: str = 'unified_hotel_database.json'):
        """保存数据到JSON文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"数据已保存到文件: {filename}")
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")
            raise

    def print_statistics(self):
        """打印处理统计信息"""
        logger.info("=" * 50)
        logger.info("处理统计信息:")
        logger.info(f"携程酒店总数: {self.stats['ctrip_total']}")
        logger.info(f"飞猪酒店总数: {self.stats['fliggy_total']}")
        logger.info(f"去重后酒店总数: {self.stats['merged_total']}")
        logger.info(f"合并的重复酒店数: {self.stats['duplicates_merged']}")
        logger.info(f"缺失英文名称的酒店数: {self.stats['missing_english_names']}")
        logger.info("=" * 50)

    def create_unified_database(self, output_filename: str = 'unified_hotel_database.json') -> List[Dict]:
        """创建统一的酒店数据库（主函数）"""
        logger.info("开始创建统一的酒店中英文名称对照数据库...")

        try:
            # 处理所有数据
            self.process_all_data()

            # 生成最终数据库
            final_database = self.generate_final_database()

            # 保存到文件
            self.save_to_json(final_database, output_filename)

            # 打印统计信息
            self.print_statistics()

            logger.info("统一酒店数据库创建完成！")
            return final_database

        except Exception as e:
            logger.error(f"创建数据库时发生错误: {str(e)}")
            raise


def main():
    """主程序入口"""
    try:
        # 创建整合器实例
        merger = HotelDatabaseMerger()

        # 创建统一数据库
        database = merger.create_unified_database()

        print(f"\n✅ 成功创建统一酒店数据库！")
        print(f"📊 总计酒店数量: {len(database)}")
        print(f"📁 输出文件: unified_hotel_database.json")
        print(f"📋 详细日志: hotel_merger.log")

    except Exception as e:
        print(f"\n❌ 程序执行失败: {str(e)}")
        logger.error(f"程序执行失败: {str(e)}")


if __name__ == "__main__":
    main()
