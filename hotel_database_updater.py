#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
酒店数据库增量更新模块

基于现有的统一酒店数据库，整合新的携程数据文件

主要功能：
1. 读取新的携程数据文件
2. 与现有数据库进行去重合并
3. 保持极简化格式输出
4. 提供详细的处理统计信息

作者：AI Assistant
创建日期：2025-07-24
"""

import json
import hashlib
import re
import os
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hotel_updater.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class HotelDatabaseUpdater:
    """酒店数据库增量更新器"""
    
    def __init__(self, existing_database_file: str = 'unified_hotel_database.json'):
        """初始化更新器"""
        self.existing_database_file = existing_database_file
        self.existing_hotels = {}  # 以中文名称为key的现有酒店字典
        self.new_hotels = []  # 新酒店列表
        self.stats = {
            'existing_total': 0,
            'new_data_total': 0,
            'duplicates_found': 0,
            'duplicates_updated': 0,
            'new_hotels_added': 0,
            'final_total': 0,
            'missing_english_names': 0
        }
        
        # 加载现有数据库
        self._load_existing_database()
    
    def _load_existing_database(self):
        """加载现有的酒店数据库"""
        try:
            if not os.path.exists(self.existing_database_file):
                logger.warning(f"现有数据库文件不存在: {self.existing_database_file}")
                logger.info("将创建新的数据库")
                return
            
            with open(self.existing_database_file, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
            
            # 转换为以中文名称为key的字典
            for hotel in existing_data:
                chinese_name = hotel.get('chinese_name', '').strip()
                if chinese_name:
                    self.existing_hotels[chinese_name] = hotel
            
            self.stats['existing_total'] = len(self.existing_hotels)
            logger.info(f"成功加载现有数据库，酒店数量: {self.stats['existing_total']}")
            
        except Exception as e:
            logger.error(f"加载现有数据库失败: {str(e)}")
            raise
    
    def load_new_ctrip_data(self, new_data_file: str) -> List[Dict]:
        """加载新的携程数据文件"""
        try:
            if not os.path.exists(new_data_file):
                logger.error(f"新数据文件不存在: {new_data_file}")
                raise FileNotFoundError(f"文件不存在: {new_data_file}")
            
            with open(new_data_file, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
            
            logger.info(f"成功加载新数据文件: {new_data_file}, 原始记录数: {len(raw_data)}")
            return raw_data
            
        except Exception as e:
            logger.error(f"加载新数据文件失败: {str(e)}")
            raise
    
    def clean_new_ctrip_data(self, raw_data: List[Dict]) -> List[Dict]:
        """清洗新的携程数据"""
        cleaned_data = []
        
        for item in raw_data:
            try:
                chinese_name = item.get('hotelName', '').strip()
                if not chinese_name:
                    continue
                
                # 提取并清洗字段
                english_name = item.get('hotel-subtitle', '').strip()
                
                hotel_data = {
                    'chinese_name': chinese_name,
                    'english_name': english_name
                }
                
                cleaned_data.append(hotel_data)
                
            except Exception as e:
                logger.warning(f"清洗携程数据时出错: {str(e)}")
                continue
        
        self.stats['new_data_total'] = len(cleaned_data)
        logger.info(f"新携程数据清洗完成，有效记录: {len(cleaned_data)}")
        return cleaned_data
    
    def merge_hotel_data(self, existing_hotel: Dict, new_hotel: Dict) -> Dict:
        """合并酒店数据，携程数据优先"""
        merged = new_hotel.copy()  # 优先使用新的携程数据
        
        # 如果新数据缺失英文名称，用现有数据补充
        if not merged.get('english_name') and existing_hotel.get('english_name'):
            merged['english_name'] = existing_hotel['english_name']
            logger.debug(f"为 '{merged['chinese_name']}' 补充英文名称: {existing_hotel['english_name']}")
        
        return merged
    
    def process_new_data(self, new_data_file: str):
        """处理新数据文件"""
        logger.info(f"开始处理新数据文件: {new_data_file}")
        
        # 加载并清洗新数据
        raw_data = self.load_new_ctrip_data(new_data_file)
        cleaned_data = self.clean_new_ctrip_data(raw_data)
        
        # 处理每条新数据
        for new_hotel in cleaned_data:
            chinese_name = new_hotel['chinese_name']
            
            if chinese_name in self.existing_hotels:
                # 发现重复，合并数据
                self.stats['duplicates_found'] += 1
                
                existing_hotel = self.existing_hotels[chinese_name]
                merged_hotel = self.merge_hotel_data(existing_hotel, new_hotel)
                
                # 检查是否有实际更新
                if merged_hotel != existing_hotel:
                    self.existing_hotels[chinese_name] = merged_hotel
                    self.stats['duplicates_updated'] += 1
                    logger.debug(f"更新酒店: {chinese_name}")
                
            else:
                # 新酒店，直接添加
                self.existing_hotels[chinese_name] = new_hotel
                self.stats['new_hotels_added'] += 1
                logger.debug(f"新增酒店: {chinese_name}")
        
        self.stats['final_total'] = len(self.existing_hotels)
        logger.info(f"数据处理完成，最终酒店总数: {self.stats['final_total']}")
    
    def generate_final_database(self) -> List[Dict]:
        """生成最终的数据库"""
        final_database = []
        
        for chinese_name, hotel_data in self.existing_hotels.items():
            # 检查英文名称是否缺失
            english_name = hotel_data.get('english_name', '').strip()
            if not english_name:
                english_name = ""
                self.stats['missing_english_names'] += 1
            
            # 构建最终记录 - 极简化版本
            final_record = {
                'chinese_name': chinese_name,
                'english_name': english_name
            }
            
            final_database.append(final_record)
        
        # 按中文名称排序
        final_database.sort(key=lambda x: x['chinese_name'])
        
        logger.info(f"最终数据库生成完成，记录数: {len(final_database)}")
        return final_database
    
    def save_updated_database(self, output_file: str = None):
        """保存更新后的数据库"""
        if output_file is None:
            output_file = self.existing_database_file
        
        final_database = self.generate_final_database()
        
        try:
            # 备份原文件
            if os.path.exists(output_file):
                backup_file = f"{output_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                os.rename(output_file, backup_file)
                logger.info(f"原数据库已备份为: {backup_file}")
            
            # 保存新数据库
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(final_database, f, ensure_ascii=False, indent=2)
            
            logger.info(f"更新后的数据库已保存到: {output_file}")
            return final_database
            
        except Exception as e:
            logger.error(f"保存数据库失败: {str(e)}")
            raise
    
    def print_statistics(self):
        """打印处理统计信息"""
        logger.info("=" * 60)
        logger.info("📊 数据库更新统计信息:")
        logger.info(f"📁 原有酒店数量: {self.stats['existing_total']:,}")
        logger.info(f"📥 新数据记录数: {self.stats['new_data_total']:,}")
        logger.info(f"🔍 发现重复酒店: {self.stats['duplicates_found']:,}")
        logger.info(f"🔄 实际更新酒店: {self.stats['duplicates_updated']:,}")
        logger.info(f"➕ 新增酒店数量: {self.stats['new_hotels_added']:,}")
        logger.info(f"📊 最终酒店总数: {self.stats['final_total']:,}")
        logger.info(f"❌ 缺失英文名称: {self.stats['missing_english_names']:,}")
        logger.info(f"📈 增长率: {(self.stats['new_hotels_added']/self.stats['existing_total']*100):.1f}%")
        logger.info("=" * 60)
    
    def update_database(self, new_data_file: str, output_file: str = None) -> List[Dict]:
        """更新数据库（主函数）"""
        logger.info("🚀 开始更新酒店数据库...")
        
        try:
            # 处理新数据
            self.process_new_data(new_data_file)
            
            # 保存更新后的数据库
            final_database = self.save_updated_database(output_file)
            
            # 打印统计信息
            self.print_statistics()
            
            logger.info("✅ 数据库更新完成！")
            return final_database
            
        except Exception as e:
            logger.error(f"更新数据库时发生错误: {str(e)}")
            raise


def main():
    """主程序入口"""
    try:
        # 创建更新器实例
        updater = HotelDatabaseUpdater()
        
        # 更新数据库
        new_data_file = 'ctrip-2025-07-24sg.json'
        updated_database = updater.update_database(new_data_file)
        
        print(f"\n✅ 数据库更新成功！")
        print(f"📊 最终酒店数量: {len(updated_database):,}")
        print(f"📁 输出文件: unified_hotel_database.json")
        print(f"📋 详细日志: hotel_updater.log")
        
    except Exception as e:
        print(f"\n❌ 程序执行失败: {str(e)}")
        logger.error(f"程序执行失败: {str(e)}")


if __name__ == "__main__":
    main()
