#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
酒店数据库整合模块配置文件

配置项：
- 输入文件路径
- 输出文件设置
- 数据处理规则

作者：AI Assistant
创建日期：2025-07-24
"""

# 输入文件配置
INPUT_FILES = {
    'ctrip': [
        'ctrip-2025-07-21.json',
        'ctrip-2025-07-22.json'
    ],
    'fliggy': [
        'fliggy-2025-07-22.json',
        'fliggy-2025-07-22 (1).json'
    ]
}

# 输出文件配置
OUTPUT_CONFIG = {
    'database_file': 'unified_hotel_database.json',
    'log_file': 'hotel_merger.log',
    'encoding': 'utf-8'
}

# 字段映射配置
FIELD_MAPPING = {
    'ctrip': {
        'chinese_name': 'hotelName',
        'english_name': 'hotel-subtitle',
        'score': 'score',
        'location': 'position-desc'
    },
    'fliggy': {
        'chinese_name': 'row-title',  # 需要清洗
        'english_name': 'ename',      # 需要清洗
        'score': 'score',             # 需要清洗
        'location': 'area'
    }
}

# 数据清洗规则
CLEANING_RULES = {
    'chinese_name_pattern': r'^([^(]+)(?:\s*\([^)]*\))?$',  # 去除括号内容
    'english_name_pattern': r'^\((.+)\)$',                  # 去除外层括号
    'score_pattern': r'^(\d+(?:\.\d+)?)',                   # 提取数字评分
    'remove_empty_fields': True,                            # 移除空字段
    'normalize_whitespace': True                             # 标准化空白字符
}

# 合并优先级规则
MERGE_PRIORITY = {
    'primary_source': 'ctrip',      # 主要数据源
    'secondary_source': 'fliggy',   # 补充数据源
    'merge_strategy': 'complement'   # 补充策略：主要数据源优先，次要数据源补充缺失字段
}

# 数据验证规则
VALIDATION_RULES = {
    'required_fields': ['chinese_name'],  # 必需字段
    'optional_fields': ['english_name', 'score', 'location'],  # 可选字段
    'max_chinese_name_length': 200,      # 中文名称最大长度
    'max_english_name_length': 300,      # 英文名称最大长度
    'score_range': (0.0, 5.0)            # 评分范围
}

# 统计配置
STATISTICS_CONFIG = {
    'enable_detailed_stats': True,       # 启用详细统计
    'track_merge_operations': True,      # 跟踪合并操作
    'log_processing_time': True          # 记录处理时间
}
