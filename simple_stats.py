#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单统计脚本 - 验证极简化酒店数据库

功能：
1. 统计总记录数
2. 统计缺失英文名称的记录数
3. 显示示例记录

作者：AI Assistant
创建日期：2025-07-24
"""

import json

def analyze_hotel_database():
    """分析酒店数据库"""
    try:
        # 读取数据库文件
        with open('unified_hotel_database.json', 'r', encoding='utf-8') as f:
            hotels = json.load(f)
        
        # 基本统计
        total_hotels = len(hotels)
        missing_english = sum(1 for hotel in hotels if not hotel.get('english_name', '').strip())
        has_english = total_hotels - missing_english
        
        print("🏨 酒店数据库统计报告（极简版）")
        print("=" * 50)
        print(f"📊 总酒店数量: {total_hotels:,}")
        print(f"✅ 有英文名称: {has_english:,} ({has_english/total_hotels*100:.1f}%)")
        print(f"❌ 缺失英文名称: {missing_english:,} ({missing_english/total_hotels*100:.1f}%)")
        print()
        
        # 显示示例记录
        print("📋 示例记录:")
        print("-" * 30)
        
        # 显示前5个有英文名称的记录
        print("✅ 有英文名称的示例:")
        count = 0
        for hotel in hotels:
            if hotel.get('english_name', '').strip() and count < 5:
                print(f"  {hotel['chinese_name']} → {hotel['english_name']}")
                count += 1
        
        print()
        
        # 显示前5个缺失英文名称的记录
        print("❌ 缺失英文名称的示例:")
        count = 0
        for hotel in hotels:
            if not hotel.get('english_name', '').strip() and count < 5:
                print(f"  {hotel['chinese_name']} → (无英文名称)")
                count += 1
        
        print()
        print("✅ 数据库分析完成！")
        
    except FileNotFoundError:
        print("❌ 错误：找不到 unified_hotel_database.json 文件")
        print("请先运行 hotel_database_merger.py 生成数据库")
    except json.JSONDecodeError:
        print("❌ 错误：JSON文件格式错误")
    except Exception as e:
        print(f"❌ 错误：{str(e)}")

if __name__ == "__main__":
    analyze_hotel_database()
