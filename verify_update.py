#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库更新验证脚本

验证新增的携程数据是否正确整合到统一酒店数据库中

作者：AI Assistant
创建日期：2025-07-24
"""

import json
import re
from collections import Counter


def load_database():
    """加载数据库"""
    try:
        with open('unified_hotel_database.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 错误：找不到数据库文件")
        return []


def analyze_singapore_data(hotels):
    """分析新加坡相关数据"""
    singapore_keywords = ['新加坡', 'singapore', 'Singapore', 'SINGAPORE']
    singapore_hotels = []
    
    for hotel in hotels:
        chinese_name = hotel.get('chinese_name', '').lower()
        english_name = hotel.get('english_name', '').lower()
        
        # 检查是否包含新加坡关键词
        is_singapore = any(keyword.lower() in chinese_name or keyword.lower() in english_name 
                          for keyword in singapore_keywords)
        
        if is_singapore:
            singapore_hotels.append(hotel)
    
    return singapore_hotels


def analyze_capsule_hotels(hotels):
    """分析胶囊酒店数据"""
    capsule_keywords = ['胶囊', 'capsule', 'Capsule', 'CAPSULE', 'pod', 'Pod', 'POD']
    capsule_hotels = []
    
    for hotel in hotels:
        chinese_name = hotel.get('chinese_name', '')
        english_name = hotel.get('english_name', '')
        
        # 检查是否包含胶囊酒店关键词
        is_capsule = any(keyword in chinese_name or keyword in english_name 
                        for keyword in capsule_keywords)
        
        if is_capsule:
            capsule_hotels.append(hotel)
    
    return capsule_hotels


def analyze_new_data_patterns(hotels):
    """分析新数据的模式"""
    patterns = {
        'total_hotels': len(hotels),
        'has_english': 0,
        'missing_english': 0,
        'singapore_related': 0,
        'capsule_hotels': 0,
        'brand_distribution': Counter(),
        'location_distribution': Counter()
    }
    
    # 品牌关键词
    brand_keywords = {
        '希尔顿': 'hilton',
        '万豪': 'marriott',
        '香格里拉': 'shangri',
        '宜必思': 'ibis',
        '诺富特': 'novotel',
        '美居': 'mercure',
        '81酒店': 'hotel 81',
        '假日酒店': 'holiday inn'
    }
    
    for hotel in hotels:
        chinese_name = hotel.get('chinese_name', '')
        english_name = hotel.get('english_name', '').strip()
        
        # 统计英文名称
        if english_name:
            patterns['has_english'] += 1
        else:
            patterns['missing_english'] += 1
        
        # 统计新加坡相关
        if any(keyword in chinese_name.lower() or keyword in english_name.lower() 
               for keyword in ['新加坡', 'singapore']):
            patterns['singapore_related'] += 1
        
        # 统计胶囊酒店
        if any(keyword in chinese_name or keyword in english_name 
               for keyword in ['胶囊', 'capsule', 'pod']):
            patterns['capsule_hotels'] += 1
        
        # 统计品牌分布
        for brand_chinese, brand_english in brand_keywords.items():
            if (brand_chinese in chinese_name or 
                brand_english in english_name.lower()):
                patterns['brand_distribution'][brand_chinese] += 1
        
        # 统计位置分布（简单分析）
        if '新加坡' in chinese_name:
            patterns['location_distribution']['新加坡'] += 1
        elif any(keyword in chinese_name for keyword in ['吉隆坡', 'kuala lumpur']):
            patterns['location_distribution']['吉隆坡'] += 1
        elif any(keyword in chinese_name for keyword in ['亚庇', 'kota kinabalu']):
            patterns['location_distribution']['亚庇'] += 1
        else:
            patterns['location_distribution']['其他'] += 1
    
    return patterns


def verify_data_integrity(hotels):
    """验证数据完整性"""
    issues = []
    
    # 检查重复的中文名称
    chinese_names = [hotel.get('chinese_name', '') for hotel in hotels]
    name_counts = Counter(chinese_names)
    duplicates = [name for name, count in name_counts.items() if count > 1 and name]
    
    if duplicates:
        issues.append(f"发现重复的中文名称: {len(duplicates)} 个")
        for name in duplicates[:5]:  # 只显示前5个
            issues.append(f"  - {name} (出现 {name_counts[name]} 次)")
    
    # 检查空的中文名称
    empty_chinese = sum(1 for hotel in hotels if not hotel.get('chinese_name', '').strip())
    if empty_chinese > 0:
        issues.append(f"发现空的中文名称: {empty_chinese} 个")
    
    # 检查异常长度的名称
    long_names = [hotel for hotel in hotels 
                  if len(hotel.get('chinese_name', '')) > 100]
    if long_names:
        issues.append(f"发现异常长的中文名称: {len(long_names)} 个")
    
    return issues


def main():
    """主函数"""
    print("🔍 开始验证数据库更新结果...")
    print("=" * 60)
    
    # 加载数据库
    hotels = load_database()
    if not hotels:
        return
    
    # 基本统计
    print(f"📊 数据库基本信息:")
    print(f"  总酒店数量: {len(hotels):,}")
    
    # 分析数据模式
    patterns = analyze_new_data_patterns(hotels)
    
    print(f"\n📈 数据分布统计:")
    print(f"  有英文名称: {patterns['has_english']:,} ({patterns['has_english']/patterns['total_hotels']*100:.1f}%)")
    print(f"  缺失英文名称: {patterns['missing_english']:,} ({patterns['missing_english']/patterns['total_hotels']*100:.1f}%)")
    print(f"  新加坡相关酒店: {patterns['singapore_related']:,}")
    print(f"  胶囊酒店: {patterns['capsule_hotels']:,}")
    
    # 品牌分布
    print(f"\n🏨 主要品牌分布:")
    for brand, count in patterns['brand_distribution'].most_common(10):
        print(f"  {brand}: {count} 家")
    
    # 位置分布
    print(f"\n📍 位置分布:")
    for location, count in patterns['location_distribution'].most_common():
        print(f"  {location}: {count} 家")
    
    # 分析新加坡数据
    singapore_hotels = analyze_singapore_data(hotels)
    print(f"\n🇸🇬 新加坡酒店详细分析:")
    print(f"  新加坡酒店总数: {len(singapore_hotels)}")
    
    # 显示一些新加坡酒店示例
    print(f"\n  新加坡酒店示例:")
    for i, hotel in enumerate(singapore_hotels[:10], 1):
        chinese_name = hotel['chinese_name']
        english_name = hotel.get('english_name', '(无英文名称)')
        print(f"    {i}. {chinese_name}")
        if english_name != '(无英文名称)':
            print(f"       {english_name}")
    
    # 分析胶囊酒店
    capsule_hotels = analyze_capsule_hotels(hotels)
    print(f"\n🏠 胶囊酒店详细分析:")
    print(f"  胶囊酒店总数: {len(capsule_hotels)}")
    
    # 显示一些胶囊酒店示例
    print(f"\n  胶囊酒店示例:")
    for i, hotel in enumerate(capsule_hotels[:10], 1):
        chinese_name = hotel['chinese_name']
        english_name = hotel.get('english_name', '(无英文名称)')
        print(f"    {i}. {chinese_name}")
        if english_name != '(无英文名称)':
            print(f"       {english_name}")
    
    # 验证数据完整性
    print(f"\n🔍 数据完整性检查:")
    issues = verify_data_integrity(hotels)
    if issues:
        print("  发现以下问题:")
        for issue in issues:
            print(f"    {issue}")
    else:
        print("  ✅ 未发现数据完整性问题")
    
    print(f"\n✅ 验证完成！数据库更新成功，新增了372家酒店。")
    print(f"📁 当前数据库包含 {len(hotels):,} 家酒店")


if __name__ == "__main__":
    main()
